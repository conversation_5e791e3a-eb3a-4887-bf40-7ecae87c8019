version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - sqlite_data:/app/data
    environment:
      - DEBUG=1
      - DJANGO_SETTINGS_MODULE=loanmanager.settings
    command: python manage.py runserver 0.0.0.0:8000
    depends_on:
      - db-init

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    command: npm run dev

  # Service to initialize SQLite database directory
  db-init:
    image: alpine:latest
    volumes:
      - sqlite_data:/data
    command: sh -c "mkdir -p /data && chmod 755 /data"

volumes:
  sqlite_data:
