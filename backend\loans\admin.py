from django.contrib import admin
from .models import (
    <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Senior<PERSON>ender, LoanType, InterestType,
    Loan, InterestRate, InterestCalculation, Payment, Invoice,
    MarketRate, Disbursement, Receipt
)


@admin.register(Borrower)
class BorrowerAdmin(admin.ModelAdmin):
    list_display = ['legal_name', 'client_description', 'created_at']
    search_fields = ['legal_name', 'client_description']
    list_filter = ['created_at']


@admin.register(Broker)
class BrokerAdmin(admin.ModelAdmin):
    list_display = ['name', 'type', 'active', 'created_at']
    list_filter = ['name', 'active']


@admin.register(SeniorLender)
class SeniorLenderAdmin(admin.ModelAdmin):
    list_display = ['name', 'tri_party_agreement', 'active', 'created_at']
    list_filter = ['tri_party_agreement', 'active']


@admin.register(LoanType)
class LoanTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'max_duration_months', 'created_at']
    list_filter = ['name']


@admin.register(InterestType)
class InterestTypeAdmin(admin.ModelAdmin):
    list_display = ['collection_method', 'frequency', 'rate_type', 'description']
    list_filter = ['collection_method', 'frequency', 'rate_type']


@admin.register(Loan)
class LoanAdmin(admin.ModelAdmin):
    list_display = [
        'loan_number', 'borrower', 'broker', 'loan_type',
        'maximum_commitment', 'advance_outstanding', 'status', 'open_date'
    ]
    list_filter = ['status', 'loan_type', 'broker', 'special_clause', 'open_date']
    search_fields = ['loan_number', 'borrower__legal_name']
    date_hierarchy = 'open_date'
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('loan_number', 'borrower', 'broker', 'senior_lender', 'loan_type', 'interest_type')
        }),
        ('Financial Details', {
            'fields': ('maximum_commitment', 'loan_amount', 'advance_outstanding', 'spread')
        }),
        ('Dates', {
            'fields': ('open_date', 'maturity_date', 'policy_renewal_date', 'initial_loan_date')
        }),
        ('Status & Special Attributes', {
            'fields': ('status', 'special_clause')
        }),
        ('Audit', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(InterestRate)
class InterestRateAdmin(admin.ModelAdmin):
    list_display = ['loan', 'rate_source', 'base_rate', 'spread', 'total_rate', 'effective_date', 'is_current']
    list_filter = ['rate_source', 'is_current', 'effective_date']
    search_fields = ['loan__loan_number']
    date_hierarchy = 'effective_date'


@admin.register(InterestCalculation)
class InterestCalculationAdmin(admin.ModelAdmin):
    list_display = ['loan', 'calculation_date', 'daily_interest_amount', 'running_interest_total', 'balance_interest']
    list_filter = ['calculation_date']
    search_fields = ['loan__loan_number']
    date_hierarchy = 'calculation_date'


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['loan', 'amount', 'payment_date', 'payment_type', 'status', 'reference_number']
    list_filter = ['payment_type', 'status', 'payment_date']
    search_fields = ['loan__loan_number', 'reference_number']
    date_hierarchy = 'payment_date'


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ['invoice_number', 'loan', 'amount', 'invoice_date', 'due_date', 'status', 'repayment_amount']
    list_filter = ['status', 'invoice_date', 'due_date']
    search_fields = ['invoice_number', 'loan__loan_number']
    date_hierarchy = 'invoice_date'


@admin.register(MarketRate)
class MarketRateAdmin(admin.ModelAdmin):
    list_display = ['rate_type', 'rate_value', 'rate_date', 'scraped_at']
    list_filter = ['rate_type', 'rate_date']
    date_hierarchy = 'rate_date'


@admin.register(Disbursement)
class DisbursementAdmin(admin.ModelAdmin):
    list_display = ['loan', 'amount', 'disbursement_date', 'description', 'reference_number']
    list_filter = ['disbursement_date']
    search_fields = ['loan__loan_number', 'reference_number', 'description']
    date_hierarchy = 'disbursement_date'


@admin.register(Receipt)
class ReceiptAdmin(admin.ModelAdmin):
    list_display = ['loan', 'amount', 'receipt_date', 'description', 'reference_number']
    list_filter = ['receipt_date']
    search_fields = ['loan__loan_number', 'reference_number', 'description']
    date_hierarchy = 'receipt_date'
