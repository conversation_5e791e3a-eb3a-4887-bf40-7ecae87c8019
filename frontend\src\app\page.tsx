'use client'

import { <PERSON><PERSON>, <PERSON>, Typo<PERSON>, Space } from 'antd'
import { PlusOutlined, FileTextOutlined, BarChartOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography

export default function Home() {
  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={1}>Loan Manager</Title>
      <Paragraph>
        Professional loan management software with Microsoft Office integration
      </Paragraph>
      
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px', marginTop: '32px' }}>
        <Card
          title="Add New Loan"
          extra={<PlusOutlined />}
          hoverable
          onClick={() => console.log('Add loan clicked')}
        >
          <p>Create a new loan application with all required documentation</p>
        </Card>
        
        <Card
          title="Manage Loans"
          extra={<FileTextOutlined />}
          hoverable
          onClick={() => console.log('Manage loans clicked')}
        >
          <p>View, edit, and process existing loan applications</p>
        </Card>
        
        <Card
          title="Reports & Analytics"
          extra={<BarChartOutlined />}
          hoverable
          onClick={() => console.log('Reports clicked')}
        >
          <p>Generate reports and analyze loan portfolio performance</p>
        </Card>
      </div>
      
      <div style={{ marginTop: '48px', textAlign: 'center' }}>
        <Space>
          <Button type="primary" size="large" icon={<PlusOutlined />}>
            Quick Add Loan
          </Button>
          <Button size="large">
            View All Loans
          </Button>
        </Space>
      </div>
    </div>
  )
}
