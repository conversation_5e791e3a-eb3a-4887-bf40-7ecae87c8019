from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal


class Borrower(models.Model):
    """Legal borrowers in the system"""
    legal_name = models.CharField(max_length=255, unique=True)
    client_description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'borrowers'
        ordering = ['legal_name']

    def __str__(self):
        return self.legal_name


class Broker(models.Model):
    """Brokers managing loans"""
    BROKER_CHOICES = [
        ('targeted_strategies', 'Targeted Strategies'),
        ('stalwart', 'Stalwart'),
        ('kohr', 'KOHR'),
    ]

    name = models.CharField(max_length=100, choices=BROKER_CHOICES, unique=True)
    type = models.CharField(max_length=50, blank=True)
    active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'brokers'
        ordering = ['name']

    def __str__(self):
        return self.get_name_display()


class SeniorLender(models.Model):
    """Senior lenders funding loans"""
    LENDER_CHOICES = [
        ('capco', 'CAPCo'),
        ('bns', 'BNS'),
        ('equitable', 'Equitable'),
        ('bmo', 'BMO'),
        ('cibc', 'CIBC'),
        ('td', 'Toronto Dominion Bank'),
    ]

    name = models.CharField(max_length=100, choices=LENDER_CHOICES, unique=True)
    tri_party_agreement = models.BooleanField(default=False)
    active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'senior_lenders'
        ordering = ['name']

    def __str__(self):
        return self.get_name_display()


class LoanType(models.Model):
    """Types of loans available"""
    LOAN_TYPE_CHOICES = [
        ('demand', 'Demand Loan'),
        ('term', 'Term Loan'),
        ('bridge', 'Bridge Loan'),
        ('promissory_note', 'Promissory Note'),
    ]

    name = models.CharField(max_length=50, choices=LOAN_TYPE_CHOICES, unique=True)
    description = models.TextField()
    max_duration_months = models.PositiveIntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'loan_types'
        ordering = ['name']

    def __str__(self):
        return self.get_name_display()


class InterestType(models.Model):
    """Interest collection methods and types"""
    COLLECTION_CHOICES = [
        ('advance', 'Advance'),
        ('arrears', 'Arrears'),
        ('capitalized', 'Capitalized'),
    ]

    FREQUENCY_CHOICES = [
        ('annually', 'Annually'),
        ('monthly', 'Monthly'),
        ('daily', 'Daily'),
    ]

    RATE_TYPE_CHOICES = [
        ('fixed', 'Fixed'),
        ('floating', 'Floating'),
    ]

    collection_method = models.CharField(max_length=20, choices=COLLECTION_CHOICES)
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES)
    rate_type = models.CharField(max_length=20, choices=RATE_TYPE_CHOICES)
    description = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'interest_types'
        unique_together = ['collection_method', 'frequency', 'rate_type']
        ordering = ['collection_method', 'frequency']

    def __str__(self):
        return f"{self.get_collection_method_display()} - {self.get_frequency_display()} ({self.get_rate_type_display()})"


class Loan(models.Model):
    """Main loan entity"""
    STATUS_CHOICES = [
        ('open', 'Open'),
        ('closed', 'Closed'),
        ('pending', 'Pending'),
        ('defaulted', 'Defaulted'),
    ]

    # Relationships
    borrower = models.ForeignKey(Borrower, on_delete=models.PROTECT, related_name='loans')
    broker = models.ForeignKey(Broker, on_delete=models.PROTECT, related_name='loans')
    senior_lender = models.ForeignKey(SeniorLender, on_delete=models.PROTECT, related_name='loans', null=True, blank=True)
    loan_type = models.ForeignKey(LoanType, on_delete=models.PROTECT, related_name='loans')
    interest_type = models.ForeignKey(InterestType, on_delete=models.PROTECT, related_name='loans')

    # Loan identification and basic info
    loan_number = models.CharField(max_length=50, unique=True)

    # Financial details
    maximum_commitment = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    loan_amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    advance_outstanding = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    spread = models.DecimalField(max_digits=5, decimal_places=4, default=Decimal('0.0000'))  # Percentage as decimal

    # Important dates
    open_date = models.DateField()
    maturity_date = models.DateField()
    policy_renewal_date = models.DateField(null=True, blank=True)
    initial_loan_date = models.DateField()

    # Special attributes
    special_clause = models.BooleanField(default=False)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open')

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'loans'
        ordering = ['-open_date', 'loan_number']
        indexes = [
            models.Index(fields=['loan_number']),
            models.Index(fields=['status']),
            models.Index(fields=['open_date']),
            models.Index(fields=['borrower', 'status']),
        ]

    def __str__(self):
        return f"{self.loan_number} - {self.borrower.legal_name}"

    @property
    def loan_base(self):
        """Calculate loan base (advance outstanding)"""
        return self.advance_outstanding

    @property
    def current_interest_rate(self):
        """Get current total interest rate"""
        current_rate = self.interest_rates.filter(is_current=True).first()
        return current_rate.total_rate if current_rate else Decimal('0.0000')


class InterestRate(models.Model):
    """Historical interest rates for loans"""
    RATE_SOURCE_CHOICES = [
        ('prime', 'Prime Rate'),
        ('corra', 'CORRA Rate'),
        ('prescribed', 'Prescribed Rate'),
        ('custom', 'Custom Rate'),
    ]

    loan = models.ForeignKey(Loan, on_delete=models.CASCADE, related_name='interest_rates')
    rate_source = models.CharField(max_length=20, choices=RATE_SOURCE_CHOICES)
    base_rate = models.DecimalField(max_digits=5, decimal_places=4)  # Percentage as decimal
    spread = models.DecimalField(max_digits=5, decimal_places=4, default=Decimal('0.0000'))
    total_rate = models.DecimalField(max_digits=5, decimal_places=4)  # base_rate + spread
    effective_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    is_current = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'interest_rates'
        ordering = ['-effective_date']
        indexes = [
            models.Index(fields=['loan', 'effective_date']),
            models.Index(fields=['is_current']),
        ]

    def save(self, *args, **kwargs):
        # Calculate total rate
        self.total_rate = self.base_rate + self.spread
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.loan.loan_number} - {self.total_rate}% ({self.effective_date})"


class InterestCalculation(models.Model):
    """Daily interest calculations for loans"""
    loan = models.ForeignKey(Loan, on_delete=models.CASCADE, related_name='interest_calculations')
    calculation_date = models.DateField()
    daily_interest_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    running_interest_total = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    accrued_interest = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    deferred_interest = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    interest_received = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    balance_interest = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'interest_calculations'
        unique_together = ['loan', 'calculation_date']
        ordering = ['-calculation_date']
        indexes = [
            models.Index(fields=['loan', 'calculation_date']),
        ]

    def __str__(self):
        return f"{self.loan.loan_number} - {self.calculation_date} - ${self.daily_interest_amount}"


class Payment(models.Model):
    """Loan payments received"""
    PAYMENT_TYPE_CHOICES = [
        ('principal', 'Principal'),
        ('interest', 'Interest'),
        ('both', 'Principal & Interest'),
        ('fee', 'Fee'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    loan = models.ForeignKey(Loan, on_delete=models.CASCADE, related_name='payments')
    amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    payment_date = models.DateField()
    due_date = models.DateField()
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPE_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    reference_number = models.CharField(max_length=100, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'payments'
        ordering = ['-payment_date']
        indexes = [
            models.Index(fields=['loan', 'payment_date']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.loan.loan_number} - ${self.amount} ({self.payment_date})"


class Invoice(models.Model):
    """Invoice tracking for MTM and EINS"""
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('paid', 'Paid'),
        ('overdue', 'Overdue'),
        ('cancelled', 'Cancelled'),
    ]

    loan = models.ForeignKey(Loan, on_delete=models.CASCADE, related_name='invoices')
    invoice_number = models.CharField(max_length=50, unique=True)
    amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    invoice_date = models.DateField()
    due_date = models.DateField()
    repayment_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    repayment_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    details = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'invoices'
        ordering = ['-invoice_date']
        indexes = [
            models.Index(fields=['invoice_number']),
            models.Index(fields=['loan', 'status']),
        ]

    def __str__(self):
        return f"{self.invoice_number} - {self.loan.loan_number} - ${self.amount}"

    @property
    def is_overdue(self):
        """Check if invoice is overdue"""
        from django.utils import timezone
        return self.due_date < timezone.now().date() and self.status not in ['paid', 'cancelled']


class MarketRate(models.Model):
    """Historical market rates from web scraping"""
    RATE_TYPE_CHOICES = [
        ('prime', 'Prime Rate'),
        ('corra', 'CORRA Rate'),
        ('sunlife_dividend', 'Sunlife Dividend Scale'),
        ('prescribed', 'Canadian Prescribed Interest Rate'),
    ]

    rate_type = models.CharField(max_length=30, choices=RATE_TYPE_CHOICES)
    rate_value = models.DecimalField(max_digits=5, decimal_places=4)  # Percentage as decimal
    rate_date = models.DateField()
    source_url = models.URLField()
    scraped_at = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)

    class Meta:
        db_table = 'market_rates'
        unique_together = ['rate_type', 'rate_date']
        ordering = ['-rate_date', 'rate_type']
        indexes = [
            models.Index(fields=['rate_type', 'rate_date']),
        ]

    def __str__(self):
        return f"{self.get_rate_type_display()} - {self.rate_value}% ({self.rate_date})"


class Disbursement(models.Model):
    """Loan disbursements to borrowers"""
    loan = models.ForeignKey(Loan, on_delete=models.CASCADE, related_name='disbursements')
    amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    disbursement_date = models.DateField()
    description = models.TextField()
    reference_number = models.CharField(max_length=100, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'disbursements'
        ordering = ['-disbursement_date']
        indexes = [
            models.Index(fields=['loan', 'disbursement_date']),
        ]

    def __str__(self):
        return f"{self.loan.loan_number} - Disbursement ${self.amount} ({self.disbursement_date})"


class Receipt(models.Model):
    """Loan receipts from borrowers"""
    loan = models.ForeignKey(Loan, on_delete=models.CASCADE, related_name='receipts')
    amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    receipt_date = models.DateField()
    description = models.TextField()
    reference_number = models.CharField(max_length=100, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'receipts'
        ordering = ['-receipt_date']
        indexes = [
            models.Index(fields=['loan', 'receipt_date']),
        ]

    def __str__(self):
        return f"{self.loan.loan_number} - Receipt ${self.amount} ({self.receipt_date})"
